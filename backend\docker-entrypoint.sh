#!/bin/sh
###
 # @Author: JustSOOw <EMAIL>
 # @Date: 2025-06-29 23:48:37
 # @LastEditors: JustSOOw <EMAIL>
 # @LastEditTime: 2025-06-30 00:51:37
 # @FilePath: \XItools\backend\docker-entrypoint.sh
 # @Description: Docker container startup script for XItools backend
 #
 # Copyright (c) 2025 by Furdow, All Rights Reserved.
###

# XItools backend Docker startup script
# Handle database migration and application startup

set -e

echo "Starting XItools backend service..."

# Wait for database to be ready
echo "Waiting for database connection..."
until pg_isready -h postgres -p 5432 -U postgres 2>/dev/null; do
  echo "Database not ready, waiting 5 seconds..."
  sleep 5
done

echo "Database connection successful"

# Run database migrations
echo "Running database migrations..."
echo "📋 检查migrations目录内容:"
ls -la ./prisma/migrations/ 2>/dev/null || echo "⚠️ migrations目录不存在"

# 尝试运行迁移
if npx prisma migrate deploy; then
  echo "✅ 数据库迁移成功"
else
  echo "⚠️ 迁移失败，尝试使用 db push 作为备用方案..."
  if npx prisma db push; then
    echo "✅ 数据库schema推送成功"
  else
    echo "❌ 数据库初始化完全失败"
    exit 1
  fi
fi

# Generate Prisma client (ensure latest)
echo "Generating Prisma client..."
npx prisma generate

echo "Starting application server..."

# Start different modes based on environment
if [ "$NODE_ENV" = "development" ]; then
  echo "Starting in development mode..."
  exec npm run dev
else
  echo "Starting in production mode..."
  exec npm start
fi
