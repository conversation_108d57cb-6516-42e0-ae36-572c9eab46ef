# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建产物
dist/
dist-electron/
build/
*.tgz
*.tar.gz

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov/
coverage/
.nyc_output/

# 依赖锁定文件（在容器中重新生成）
package-lock.json
yarn.lock

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 临时文件
tmp/
temp/

# Git相关
.git/
.gitignore

# 文档
README.md
docs/

# 测试相关
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Electron相关文件（Web版本不需要）
electron/

# 其他
.eslintrc*
.prettierrc*
tsconfig.json
tsconfig.node.json
vite.config.ts
tailwind.config.js
postcss.config.js
